// Uniforms (passed from JavaScript)
uniform float uTime;
uniform vec2 uResolution;
uniform vec3 uColor;

// Varyings (received from vertex shader)
varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;

void main() {
    // Basic UV-based gradient
    vec3 color = vec3(vUv.x, vUv.y, 0.5);

    // Add time-based animation
    float wave = sin(vUv.x * 10.0 + uTime) * 0.1;
    color.r += wave;

    // Add some lighting based on normal
    vec3 lightDirection = normalize(vec3(1.0, 1.0, 1.0));
    float lightIntensity = dot(normalize(vNormal), lightDirection);
    lightIntensity = max(lightIntensity, 0.2); // Ambient light

    color *= lightIntensity;

    // Apply uniform color if provided
    if (length(uColor) > 0.0) {
        color = mix(color, uColor, 0.5);
    }

    gl_FragColor = vec4(color, 1.0);
}