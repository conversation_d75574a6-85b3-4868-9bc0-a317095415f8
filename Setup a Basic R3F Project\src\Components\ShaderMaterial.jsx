import { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { shaderMaterial } from '@react-three/drei';
import vertexShader from './Shaders/vertexShader.glsl';
import fragmentShader from './Shaders/fragmentShader.glsl';

const MyShaderMaterial = shaderMaterial(
  {
    uTime: 0,
    uResolution: [1, 1],
    uColor: [1, 0, 0] // Optional red color
  },
  vertexShader,
  fragmentShader
)

function MyMesh() {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.material.uniforms.uTime.value = state.clock.elapsedTime
    }
  })

  return (
    <mesh ref={meshRef}>
      <planeGeometry args={[2, 2]} />
      <myShaderMaterial />
    </mesh>
  )
}