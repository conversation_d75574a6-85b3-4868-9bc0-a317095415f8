import { Canvas, use<PERSON>rame } from "@react-three/fiber";
import {  OrbitControls} from "@react-three/drei"
import Box from "./Components/Box";
import TextExample from "./Components/TextExample";
import ImageExample from "./Components/ImageExample";
import MyMesh from "./Components/ShaderMaterial";

const App = () => {
  return (
    <div style={{ width: "100%", height: "100%", position: "relative", backgroundColor: "#000" }}>
      <Canvas orthographic camera={{ position: [0, 0, 10], zoom: 100 }}>
        <ambientLight />
        {/* <pointLight position={[10, 10, 10]} /> */}
        {/* <Box/> */}
        {/* <TextExample/> */}
        {/* <ImageExample/>  */}
        <OrbitControls/>
        <MyMesh/>
      </Canvas>
    </div>
  )
}
export default App