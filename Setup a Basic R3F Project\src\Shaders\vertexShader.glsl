// Uniforms (passed from JavaScript)
uniform float uTime;
uniform vec2 uResolution;

// Varyings (passed to fragment shader)
varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;

void main() {
    // Pass UV coordinates to fragment shader
    vUv = uv;

    // Pass world position to fragment shader
    vPosition = position;

    // Pass normal to fragment shader
    vNormal = normal;

    // Basic vertex transformation
    vec4 modelPosition = modelMatrix * vec4(position, 1.0);
    vec4 viewPosition = viewMatrix * modelPosition;
    vec4 projectedPosition = projectionMatrix * viewPosition;

    gl_Position = projectedPosition;
}